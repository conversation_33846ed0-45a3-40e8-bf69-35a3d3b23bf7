{"__meta": {"id": "01K1SDPV8XSR3B23TPAQBWX777", "datetime": "2025-08-04 03:13:59", "utime": **********.709771, "method": "GET", "uri": "/cache/medium/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.172515, "end": **********.724413, "duration": 0.5518980026245117, "duration_str": "552ms", "measures": [{"label": "Booting", "start": **********.172515, "relative_start": 0, "end": **********.649101, "relative_end": **********.649101, "duration": 0.*****************, "duration_str": "477ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.649113, "relative_start": 0.*****************, "end": **********.724415, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "75.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.675548, "relative_start": 0.****************, "end": **********.681487, "relative_end": **********.681487, "duration": 0.005939006805419922, "duration_str": "5.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.706193, "relative_start": 0.****************, "end": **********.70642, "relative_end": **********.70642, "duration": 0.0002269744873046875, "duration_str": "227μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.706438, "relative_start": 0.****************, "end": **********.706451, "relative_end": **********.706451, "duration": 1.2874603271484375e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "553ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1747508977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1747508977\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1784785002 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1784785002\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-110260883 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImVHeTVJQzBIK3pnRTE0cjhka1FzYlE9PSIsInZhbHVlIjoiRXRlcUZjM0pxbVAvV2N1NW5yaXE4YTM1bGllaEljOVFOcXBybk1zUnpHRStoM05qbjJ3ZW92cTZwZlpUQk5SZ0piazhaK2pSc0NZZktzKzZ5am9LN0VlcXArRFVLT2ZWcHVlQmloNVNXQ3VaRFJtRmZ0YUYvQmNMb04wbjBWN2IiLCJtYWMiOiIyMjJjMjFkOWY5MDg5MjU5NjJmZGU4YjU4Y2Y1MDFiYjg4ZDU2MjBhNjlkOTRjODVjMTFjM2NkNTZhYTZhYzMwIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Indvb1E3WDJ3TFJCZGViT0M1a1dYenc9PSIsInZhbHVlIjoiUTk5MS9vL0JCQ0F3N3FhcXVnWExMajNCTGJYVmtTQzE1YVM4ZitZZlFXWklnTFhyZWZoNzVsOURZS2U0c0JOV2daQlZWMEdZeEdwdGdIVDlvSHNCZHNpQXgxajVKVkw0cVZZZ2xvQzdpZndYRkhuTFNWT1htcTRHdGpyRXBaUUgiLCJtYWMiOiJlYmQ4ZTVmNTI5ZDNiMzhmOTM5MjgxMDhiMGNlZTg3YjlkNGIwOTk1OTU1NDNhMTMzYzlhNjMyOGJmZjlkZmJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://mlk.test/web</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-110260883\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1662067590 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImVHeTVJQzBIK3pnRTE0cjhka1FzYlE9PSIsInZhbHVlIjoiRXRlcUZjM0pxbVAvV2N1NW5yaXE4YTM1bGllaEljOVFOcXBybk1zUnpHRStoM05qbjJ3ZW92cTZwZlpUQk5SZ0piazhaK2pSc0NZZktzKzZ5am9LN0VlcXArRFVLT2ZWcHVlQmloNVNXQ3VaRFJtRmZ0YUYvQmNMb04wbjBWN2IiLCJtYWMiOiIyMjJjMjFkOWY5MDg5MjU5NjJmZGU4YjU4Y2Y1MDFiYjg4ZDU2MjBhNjlkOTRjODVjMTFjM2NkNTZhYTZhYzMwIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Indvb1E3WDJ3TFJCZGViT0M1a1dYenc9PSIsInZhbHVlIjoiUTk5MS9vL0JCQ0F3N3FhcXVnWExMajNCTGJYVmtTQzE1YVM4ZitZZlFXWklnTFhyZWZoNzVsOURZS2U0c0JOV2daQlZWMEdZeEdwdGdIVDlvSHNCZHNpQXgxajVKVkw0cVZZZ2xvQzdpZndYRkhuTFNWT1htcTRHdGpyRXBaUUgiLCJtYWMiOiJlYmQ4ZTVmNTI5ZDNiMzhmOTM5MjgxMDhiMGNlZTg3YjlkNGIwOTk1OTU1NDNhMTMzYzlhNjMyOGJmZjlkZmJiIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662067590\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6790</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">fe98aceff7b794c22782ef99966c688a</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 02:13:59 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1045577086 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1045577086\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}