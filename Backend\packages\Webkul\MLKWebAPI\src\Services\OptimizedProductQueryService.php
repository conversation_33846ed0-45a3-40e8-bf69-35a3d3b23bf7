<?php

namespace Webkul\MLKWebAPI\Services;

use Illuminate\Support\Facades\DB;
use Webkul\Product\Repositories\ProductRepository;
use Webkul\Customer\Repositories\CustomerRepository;

class OptimizedProductQueryService
{
    protected $productRepository;
    protected $customerRepository;

    public function __construct(
        ProductRepository $productRepository,
        CustomerRepository $customerRepository
    ) {
        $this->productRepository = $productRepository;
        $this->customerRepository = $customerRepository;
    }

    /**
     * 获取优化的产品查询构建器
     * 
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getOptimizedProductQuery(array $filters = [])
    {
        $currentChannel = core()->getCurrentChannel();
        $customerGroup = $this->customerRepository->getCurrentGroup();

        return $this->productRepository->with($this->getOptimizedEagerLoads($currentChannel, $customerGroup))
            ->scopeQuery(function ($query) use ($filters, $currentChannel, $customerGroup) {
                return $this->buildOptimizedQuery($query, $filters, $currentChannel, $customerGroup);
            });
    }

    /**
     * 获取优化的预加载关联
     * 
     * @param $currentChannel
     * @param $customerGroup
     * @return array
     */
    protected function getOptimizedEagerLoads($currentChannel, $customerGroup)
    {
        return [
            // 基础关联
            'images',
            'videos',
            'categories.translations',
            'attribute_family.attributes',
            
            // 属性值和翻译 - 关键优化点，一次性加载所有翻译
            'attribute_values.attribute.translations',
            'attribute_values.attribute.options.translations',
            
            // 价格相关关联 - 过滤当前渠道和客户组
            'price_indices' => function ($query) use ($currentChannel, $customerGroup) {
                $query->where('channel_id', $currentChannel->id)
                      ->where('customer_group_id', $customerGroup->id);
            },
            'customer_group_prices' => function ($query) use ($customerGroup) {
                $query->where('customer_group_id', $customerGroup->id)
                      ->orderBy('qty', 'asc');
            },
            'catalog_rule_prices' => function ($query) use ($currentChannel, $customerGroup) {
                $query->where('channel_id', $currentChannel->id)
                      ->where('customer_group_id', $customerGroup->id)
                      ->where('rule_date', now()->format('Y-m-d'));
            },
            
            // 库存关联 - 过滤当前渠道
            'inventory_indices' => function ($query) use ($currentChannel) {
                $query->where('channel_id', $currentChannel->id);
            },
            
            // 可配置产品相关 - 优化预加载
            'super_attributes.translations',
            'super_attributes.options.translations',
            'variants' => function ($query) {
                $query->with([
                    'images',
                    'attribute_values.attribute.translations',
                    'attribute_values.attribute.options.translations',
                ]);
            },
            'variants.price_indices' => function ($query) use ($currentChannel, $customerGroup) {
                $query->where('channel_id', $currentChannel->id)
                      ->where('customer_group_id', $customerGroup->id);
            },
            'variants.customer_group_prices' => function ($query) use ($customerGroup) {
                $query->where('customer_group_id', $customerGroup->id)
                      ->orderBy('qty', 'asc');
            },
            'variants.catalog_rule_prices' => function ($query) use ($currentChannel, $customerGroup) {
                $query->where('channel_id', $currentChannel->id)
                      ->where('customer_group_id', $customerGroup->id)
                      ->where('rule_date', now()->format('Y-m-d'));
            },
            'variants.inventory_indices' => function ($query) use ($currentChannel) {
                $query->where('channel_id', $currentChannel->id);
            },
            
            // 评论关联
            'reviews'
        ];
    }

    /**
     * 构建优化的查询
     * 
     * @param $query
     * @param array $filters
     * @param $currentChannel
     * @param $customerGroup
     * @return mixed
     */
    protected function buildOptimizedQuery($query, array $filters, $currentChannel, $customerGroup)
    {
        $prefix = DB::getTablePrefix();

        // 基础查询优化
        $qb = $query->distinct()
            ->select('products.*')
            ->leftJoin('products as variants', DB::raw('COALESCE('.$prefix.'variants.parent_id, '.$prefix.'variants.id)'), '=', 'products.id')
            ->leftJoin('product_price_indices', function ($join) use ($customerGroup) {
                $join->on('products.id', '=', 'product_price_indices.product_id')
                    ->where('product_price_indices.customer_group_id', $customerGroup->id);
            });

        // 渠道过滤
        if (!empty($filters['channel_id'])) {
            $qb->leftJoin('product_channels', 'products.id', '=', 'product_channels.product_id')
                ->where('product_channels.channel_id', $filters['channel_id']);
        }

        // 分类过滤
        if (!empty($filters['category_id'])) {
            $qb->leftJoin('product_categories', 'product_categories.product_id', '=', 'products.id')
                ->whereIn('product_categories.category_id', explode(',', $filters['category_id']));
        }

        // 状态过滤
        if (isset($filters['status'])) {
            $qb->leftJoin('product_attribute_values as status_pav', function ($join) {
                $join->on('products.id', '=', 'status_pav.product_id')
                    ->where('status_pav.attribute_id', 8); // status attribute id
            })->where('status_pav.boolean_value', $filters['status']);
        }

        // 可见性过滤
        if (isset($filters['visible_individually'])) {
            $qb->leftJoin('product_attribute_values as visible_pav', function ($join) {
                $join->on('products.id', '=', 'visible_pav.product_id')
                    ->where('visible_pav.attribute_id', 7); // visible_individually attribute id
            })->where('visible_pav.boolean_value', $filters['visible_individually']);
        }

        // URL key过滤
        if (!empty($filters['url_key'])) {
            $qb->leftJoin('product_attribute_values as url_key_pav', function ($join) {
                $join->on('products.id', '=', 'url_key_pav.product_id')
                    ->where('url_key_pav.attribute_id', 3); // url_key attribute id
            })->whereNotNull('url_key_pav.text_value');
        }

        // 新品过滤
        if (!empty($filters['new'])) {
            $qb->leftJoin('product_attribute_values as new_product_attribute_values', function ($join) {
                $join->on('products.id', '=', 'new_product_attribute_values.product_id')
                    ->where('new_product_attribute_values.attribute_id', 5); // new attribute id
            })
            ->leftJoin('product_attribute_values as new_variant_attribute_values', function ($join) {
                $join->on('variants.id', '=', 'new_variant_attribute_values.product_id')
                    ->where('new_variant_attribute_values.attribute_id', 5);
            })
            ->where(function ($subQuery) {
                $subQuery->where('new_product_attribute_values.boolean_value', 1)
                        ->orWhere('new_variant_attribute_values.boolean_value', 1);
            });
        }

        // 排序优化
        if (!empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'created_at':
                    $qb->orderBy('products.created_at', $filters['order'] ?? 'desc');
                    break;
                case 'price':
                    $qb->orderBy('product_price_indices.min_price', $filters['order'] ?? 'asc');
                    break;
                case 'name':
                    $qb->leftJoin('product_attribute_values as sort_pav', function ($join) {
                        $join->on('products.id', '=', 'sort_pav.product_id')
                            ->where('sort_pav.attribute_id', 2) // name attribute id
                            ->where('sort_pav.locale', core()->getRequestedLocaleCode());
                    })->orderBy('sort_pav.text_value', $filters['order'] ?? 'asc');
                    break;
                default:
                    $qb->orderBy('products.created_at', 'desc');
            }
        } else {
            $qb->orderBy('products.created_at', 'desc');
        }

        // 分组去重
        $qb->groupBy('products.id');

        // 限制结果数量
        if (!empty($filters['limit'])) {
            $qb->limit($filters['limit']);
        }

        return $qb;
    }

    /**
     * 批量预加载翻译数据，避免N+1查询
     * 
     * @param $products
     * @return void
     */
    public function preloadTranslations($products)
    {
        if ($products->isEmpty()) {
            return;
        }

        // 收集所有需要翻译的属性ID
        $attributeIds = [];
        $optionIds = [];

        foreach ($products as $product) {
            foreach ($product->attribute_values as $attributeValue) {
                $attributeIds[] = $attributeValue->attribute_id;
                
                if ($attributeValue->attribute && $attributeValue->attribute->options) {
                    foreach ($attributeValue->attribute->options as $option) {
                        $optionIds[] = $option->id;
                    }
                }
            }
        }

        // 批量预加载属性翻译
        if (!empty($attributeIds)) {
            DB::table('attribute_translations')
                ->whereIn('attribute_id', array_unique($attributeIds))
                ->get()
                ->groupBy('attribute_id');
        }

        // 批量预加载选项翻译
        if (!empty($optionIds)) {
            DB::table('attribute_option_translations')
                ->whereIn('attribute_option_id', array_unique($optionIds))
                ->get()
                ->groupBy('attribute_option_id');
        }
    }
}
