<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. 为 attribute_translations 表添加单独的 attribute_id 索引
        Schema::table('attribute_translations', function (Blueprint $table) {
            // 检查索引是否已存在，避免重复创建
            $indexExists = collect(DB::select("SHOW INDEX FROM attribute_translations"))
                ->where('Key_name', 'idx_attribute_translations_attribute_id')
                ->isNotEmpty();
            
            if (!$indexExists) {
                $table->index('attribute_id', 'idx_attribute_translations_attribute_id');
            }
        });

        // 2. 为 attribute_option_translations 表添加单独的 attribute_option_id 索引
        Schema::table('attribute_option_translations', function (Blueprint $table) {
            $indexExists = collect(DB::select("SHOW INDEX FROM attribute_option_translations"))
                ->where('Key_name', 'idx_attribute_option_translations_option_id')
                ->isNotEmpty();
            
            if (!$indexExists) {
                $table->index('attribute_option_id', 'idx_attribute_option_translations_option_id');
            }
        });

        // 3. 为 product_attribute_values 表添加复合索引优化
        Schema::table('product_attribute_values', function (Blueprint $table) {
            // 为常用的查询模式添加复合索引
            $indexExists = collect(DB::select("SHOW INDEX FROM product_attribute_values"))
                ->where('Key_name', 'idx_product_attribute_values_product_attribute')
                ->isNotEmpty();
            
            if (!$indexExists) {
                $table->index(['product_id', 'attribute_id'], 'idx_product_attribute_values_product_attribute');
            }
            
            // 为属性值查询添加索引
            $indexExists2 = collect(DB::select("SHOW INDEX FROM product_attribute_values"))
                ->where('Key_name', 'idx_product_attribute_values_attribute_channel_locale')
                ->isNotEmpty();
            
            if (!$indexExists2) {
                $table->index(['attribute_id', 'channel', 'locale'], 'idx_product_attribute_values_attribute_channel_locale');
            }
        });

        // 4. 为 product_categories 表添加索引优化
        Schema::table('product_categories', function (Blueprint $table) {
            $indexExists = collect(DB::select("SHOW INDEX FROM product_categories"))
                ->where('Key_name', 'idx_product_categories_category_id')
                ->isNotEmpty();
            
            if (!$indexExists) {
                $table->index('category_id', 'idx_product_categories_category_id');
            }
        });

        // 5. 为 product_channels 表添加索引优化
        Schema::table('product_channels', function (Blueprint $table) {
            $indexExists = collect(DB::select("SHOW INDEX FROM product_channels"))
                ->where('Key_name', 'idx_product_channels_channel_id')
                ->isNotEmpty();
            
            if (!$indexExists) {
                $table->index('channel_id', 'idx_product_channels_channel_id');
            }
        });

        // 6. 为 products 表添加复合索引
        Schema::table('products', function (Blueprint $table) {
            // 为父子产品关系查询添加索引
            $indexExists = collect(DB::select("SHOW INDEX FROM products"))
                ->where('Key_name', 'idx_products_parent_id')
                ->isNotEmpty();
            
            if (!$indexExists) {
                $table->index('parent_id', 'idx_products_parent_id');
            }
            
            // 为产品类型查询添加索引
            $indexExists2 = collect(DB::select("SHOW INDEX FROM products"))
                ->where('Key_name', 'idx_products_type')
                ->isNotEmpty();
            
            if (!$indexExists2) {
                $table->index('type', 'idx_products_type');
            }
            
            // 为创建时间排序添加索引
            $indexExists3 = collect(DB::select("SHOW INDEX FROM products"))
                ->where('Key_name', 'idx_products_created_at')
                ->isNotEmpty();
            
            if (!$indexExists3) {
                $table->index('created_at', 'idx_products_created_at');
            }
        });

        echo "数据库索引优化完成！\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除添加的索引
        Schema::table('attribute_translations', function (Blueprint $table) {
            $table->dropIndex('idx_attribute_translations_attribute_id');
        });

        Schema::table('attribute_option_translations', function (Blueprint $table) {
            $table->dropIndex('idx_attribute_option_translations_option_id');
        });

        Schema::table('product_attribute_values', function (Blueprint $table) {
            $table->dropIndex('idx_product_attribute_values_product_attribute');
            $table->dropIndex('idx_product_attribute_values_attribute_channel_locale');
        });

        Schema::table('product_categories', function (Blueprint $table) {
            $table->dropIndex('idx_product_categories_category_id');
        });

        Schema::table('product_channels', function (Blueprint $table) {
            $table->dropIndex('idx_product_channels_channel_id');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('idx_products_parent_id');
            $table->dropIndex('idx_products_type');
            $table->dropIndex('idx_products_created_at');
        });
    }
};
