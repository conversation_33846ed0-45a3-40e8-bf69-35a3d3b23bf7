{"__meta": {"id": "01K1SGZ1E62M3PVD8A6YR4MQ4R", "datetime": "2025-08-04 04:10:53", "utime": **********.894761, "method": "GET", "uri": "/api/mlk/index", "ip": "127.0.0.1"}, "modules": {"count": 6, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (4)", "Webkul\\Attribute\\Models\\AttributeFamily (1)"], "views": [], "queries": [{"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('new', 'sort', 'limit', 'channel_id', 'status', 'visible_individually', 'url_key')", "duration": 3.04, "duration_str": "3.04s", "connection": "mlk"}, {"sql": "select * from `attributes` where `code` = 'created_at'", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "duration": 1.9, "duration_str": "1.9s", "connection": "mlk"}]}, {"name": "Webkul\\Category", "models": ["Webkul\\Category\\Models\\Category (6)", "Webkul\\Category\\Models\\CategoryTranslation (5)"], "views": [], "queries": [{"sql": "select `categories`.*, `product_categories`.`product_id` as `pivot_product_id`, `product_categories`.`category_id` as `pivot_category_id` from `categories` inner join `product_categories` on `categories`.`id` = `product_categories`.`category_id` where `product_categories`.`product_id` in (178, 184, 185, 195, 211, 219)", "duration": 3.67, "duration_str": "3.67s", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (13)", "duration": 1.73, "duration_str": "1.73s", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (5)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 2.64, "duration_str": "2.64s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.56, "duration_str": "560ms", "connection": "mlk"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 2.31, "duration_str": "2.31s", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (6)", "Webkul\\Product\\Models\\ProductImage (13)"], "views": [], "queries": [{"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `new_product_attribute_values` on `products`.`id` = `new_product_attribute_values`.`product_id` and `new_product_attribute_values`.`attribute_id` = 5 left join `product_attribute_values` as `new_variant_attribute_values` on `variants`.`id` = `new_variant_attribute_values`.`product_id` and `new_variant_attribute_values`.`attribute_id` = 5 where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 and ((`new_product_attribute_values`.`boolean_value` in ('1')) or (`new_variant_attribute_values`.`boolean_value` in ('1'))) group by `products`.`id`, `products`.`id` order by `products`.`created_at` desc limit 6 offset 0", "duration": 9.41, "duration_str": "9.41s", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (178, 184, 185, 195, 211, 219) order by `position` asc", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (178, 184, 185, 195, 211, 219) order by `position` asc", "duration": 1.98, "duration_str": "1.98s", "connection": "mlk"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (14)", "Webkul\\Theme\\Models\\ThemeCustomizationTranslation (69)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `channel_id` = 1 and `status` = 1", "duration": 0.47, "duration_str": "470ms", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (9, 14, 15, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28)", "duration": 0.52, "duration_str": "520ms", "connection": "mlk"}]}]}, "messages": {"count": 1, "messages": [{"message": "[04:10:53] LOG.error: Call to undefined relationship [attributes] on model [Webkul\\Attribute\\Models\\AttributeFamily]. {\n    \"exception\": {\n        \"model\": \"Webkul\\\\Attribute\\\\Models\\\\AttributeFamily\",\n        \"relation\": \"attributes\"\n    }\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.890565, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.435425, "end": **********.901332, "duration": 0.46590685844421387, "duration_str": "466ms", "measures": [{"label": "Booting", "start": **********.435425, "relative_start": 0, "end": **********.733599, "relative_end": **********.733599, "duration": 0.****************, "duration_str": "298ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.73361, "relative_start": 0.***************, "end": **********.901334, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.74998, "relative_start": 0.*****************, "end": **********.754853, "relative_end": **********.754853, "duration": 0.004873037338256836, "duration_str": "4.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.892986, "relative_start": 0.*****************, "end": **********.893282, "relative_end": **********.893282, "duration": 0.00029587745666503906, "duration_str": "296μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Illuminate\\Database\\Eloquent\\RelationNotFoundException", "message": "Call to undefined relationship [attributes] on model [Webkul\\Attribute\\Models\\AttributeFamily].", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Database/Eloquent/RelationNotFoundException.php", "line": 35, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1271717441 data-indent-pad=\"  \"><span class=sf-dump-note>array:57</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>878</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Database\\Eloquent\\RelationNotFoundException</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">[object Webkul\\Attribute\\Models\\AttributeFamily]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">attributes</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Relations/Relation.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\Database\\Eloquent\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>874</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">noConstraints</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Database\\Eloquent\\Relations\\Relation</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>848</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getRelation</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">attributes</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>828</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"17 characters\">eagerLoadRelation</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Attribute\\Models\\AttributeFamily\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Attribute\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeFamily</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23082 title=\"2 occurrences\">#3082</a><samp data-depth=5 id=sf-dump-1271717441-ref23082 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"18 characters\">attribute_families</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Case</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Case</span>\"\n            \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>is_user_defined</span>\" => <span class=sf-dump-num>1</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Case</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Case</span>\"\n            \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>is_user_defined</span>\" => <span class=sf-dump-num>1</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n      </samp>]\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">attributes</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>794</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">eagerLoadRelations</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Attribute\\Models\\AttributeFamily\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Attribute\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeFamily</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23082 title=\"2 occurrences\">#3082</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Relations/Relation.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>213</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Relations/Relation.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Database\\Eloquent\\Relations\\Relation</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>859</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">getEager</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Database\\Eloquent\\Relations\\Relation</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>828</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"17 characters\">eagerLoadRelation</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22878 title=\"2 occurrences\">#2878</a><samp data-depth=5 id=sf-dump-1271717441-ref22878 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>219</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"13 characters\">8034412526218</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">configurable</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:36:04</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:36:04</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>219</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"13 characters\">8034412526218</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">configurable</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:36:04</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:36:04</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>additional</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>images</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2886</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3015</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>185</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/219/DUKogdoyLqHofRu9129VtLOMIJcE7E9DqJvxVyJp.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>219</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>185</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/219/DUKogdoyLqHofRu9129VtLOMIJcE7E9DqJvxVyJp.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>219</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3012</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>186</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/219/iu5Amxao7zkYv7w7VdXTwjvRUPZnLIWOTVRvp1iZ.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>219</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>2</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>186</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/219/iu5Amxao7zkYv7w7VdXTwjvRUPZnLIWOTVRvp1iZ.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>219</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>2</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>videos</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2972</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>categories</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Kalnoy\\Nestedset\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Kalnoy\\Nestedset</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3022</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref>#3049</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">translations</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                    \"<span class=sf-dump-key>pivot_product_id</span>\" => <span class=sf-dump-num>219</span>\n                    \"<span class=sf-dump-key>pivot_category_id</span>\" => <span class=sf-dump-num>13</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">logo_url</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">banner_url</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#3027</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"18 characters\">product_categories</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>219</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>219</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22859 title=\"6 occurrences\">#2859</a><samp data-depth=12 id=sf-dump-1271717441-ref22859 class=sf-dump-compact>\n                        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>\n                        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">original</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>additional</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">attribute_family_id</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">typeInstance</span>: <span class=sf-dump-const>null</span>\n                      </samp>}\n                      +<span class=sf-dump-public title=\"Public property\">pivotRelated</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22926 title=\"6 occurrences\">#2926</a><samp data-depth=12 id=sf-dump-1271717441-ref22926 class=sf-dump-compact>\n                        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">with</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">translations</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>\n                        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">original</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">logo_url</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">banner_url</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">display_mode</span>\"\n                          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">additional</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>\n                        +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"\n                          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"\n                          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">meta_keywords</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">pending</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">moved</span>: <span class=sf-dump-const>false</span>\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                    </samp>}\n                    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3042</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23063 title=\"6 occurrences\">#3063</a><samp data-depth=13 id=sf-dump-1271717441-ref23063 class=sf-dump-compact>\n                          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"21 characters\">category_translations</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=14 class=sf-dump-compact>\n                            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>63</span>\n                            \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">CASES FOR SMARTPHONE</span>\"\n                            \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"20 characters\">cases-for-smartphone</span>\"\n                            \"<span class=sf-dump-key>url_path</span>\" => \"\"\n                            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"27 characters\">&lt;p&gt;CASES FOR SMARTPHONE&lt;/p&gt;</span>\"\n                            \"<span class=sf-dump-key>meta_title</span>\" => \"\"\n                            \"<span class=sf-dump-key>meta_description</span>\" => \"\"\n                            \"<span class=sf-dump-key>meta_keywords</span>\" => \"\"\n                            \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>3</span>\n                             &#8230;1\n                          </samp>]\n                          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [ &#8230;11]\n                          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]\n                          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]\n                        </samp>}\n                        <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23087 title=\"6 occurrences\">#3087</a><samp data-depth=13 id=sf-dump-1271717441-ref23087 class=sf-dump-compact>\n                          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"21 characters\">category_translations</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [ &#8230;11]\n                          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [ &#8230;11]\n                          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]\n                          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]\n                        </samp>}\n                        <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23086 title=\"6 occurrences\">#3086</a><samp data-depth=13 id=sf-dump-1271717441-ref23086 class=sf-dump-compact>\n                          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"21 characters\">category_translations</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [ &#8230;11]\n                          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [ &#8230;11]\n                          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]\n                          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]\n                        </samp>}\n                        <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23085 title=\"6 occurrences\">#3085</a><samp data-depth=13 id=sf-dump-1271717441-ref23085 class=sf-dump-compact>\n                          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"21 characters\">category_translations</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [ &#8230;11]\n                          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [ &#8230;11]\n                          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]\n                          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]\n                        </samp>}\n                        <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23084 title=\"6 occurrences\">#3084</a><samp data-depth=13 id=sf-dump-1271717441-ref23084 class=sf-dump-compact>\n                          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"21 characters\">category_translations</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [ &#8230;11]\n                          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [ &#8230;11]\n                          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]\n                          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]\n                        </samp>}\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">display_mode</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">additional</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>\n                  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">meta_keywords</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">pending</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">moved</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>attribute_family</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">attribute_family_id</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">typeInstance</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n        <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22990 title=\"2 occurrences\">#2990</a><samp data-depth=5 id=sf-dump-1271717441-ref22990 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>211</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"13 characters\">8034412526232</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">configurable</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-03 22:17:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-03 23:43:18</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>211</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"13 characters\">8034412526232</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">configurable</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-03 22:17:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-03 23:43:18</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>additional</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>images</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2885</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3016</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>175</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/211/HNgHFdS35kfHe5KQE2xEfIt9hNB0mlXkQmm47MaC.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>211</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>175</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/211/HNgHFdS35kfHe5KQE2xEfIt9hNB0mlXkQmm47MaC.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>211</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3013</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>176</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/211/BUydp8UnzfFftXjHjAWfhY7GX27DeUmi7oFlK3yu.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>211</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>2</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>176</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/211/BUydp8UnzfFftXjHjAWfhY7GX27DeUmi7oFlK3yu.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>211</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>2</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>videos</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2855</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>categories</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Kalnoy\\Nestedset\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Kalnoy\\Nestedset</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2851</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref>#3050</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">translations</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                    \"<span class=sf-dump-key>pivot_product_id</span>\" => <span class=sf-dump-num>211</span>\n                    \"<span class=sf-dump-key>pivot_category_id</span>\" => <span class=sf-dump-num>13</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">logo_url</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">banner_url</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#3026</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"18 characters\">product_categories</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>211</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>211</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22859 title=\"6 occurrences\">#2859</a>}\n                      +<span class=sf-dump-public title=\"Public property\">pivotRelated</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22926 title=\"6 occurrences\">#2926</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                    </samp>}\n                    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3041</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23063 title=\"6 occurrences\">#3063</a>}\n                        <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23087 title=\"6 occurrences\">#3087</a>}\n                        <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23086 title=\"6 occurrences\">#3086</a>}\n                        <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23085 title=\"6 occurrences\">#3085</a>}\n                        <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23084 title=\"6 occurrences\">#3084</a>}\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">display_mode</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">additional</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>\n                  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">meta_keywords</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">pending</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">moved</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>attribute_family</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">attribute_family_id</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">typeInstance</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n        <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22989 title=\"2 occurrences\">#2989</a><samp data-depth=5 id=sf-dump-1271717441-ref22989 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>195</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">0000023684</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">configurable</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-30 18:49:41</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 16:58:55</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>195</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">0000023684</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">configurable</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-30 18:49:41</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 16:58:55</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>additional</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>images</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2871</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3017</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>169</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>195</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>169</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>195</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>videos</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2857</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>categories</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Kalnoy\\Nestedset\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Kalnoy\\Nestedset</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3035</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref>#3051</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">translations</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                    \"<span class=sf-dump-key>pivot_product_id</span>\" => <span class=sf-dump-num>195</span>\n                    \"<span class=sf-dump-key>pivot_category_id</span>\" => <span class=sf-dump-num>13</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">logo_url</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">banner_url</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#3028</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"18 characters\">product_categories</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>195</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>195</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22859 title=\"6 occurrences\">#2859</a>}\n                      +<span class=sf-dump-public title=\"Public property\">pivotRelated</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22926 title=\"6 occurrences\">#2926</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                    </samp>}\n                    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3039</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23063 title=\"6 occurrences\">#3063</a>}\n                        <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23087 title=\"6 occurrences\">#3087</a>}\n                        <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23086 title=\"6 occurrences\">#3086</a>}\n                        <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23085 title=\"6 occurrences\">#3085</a>}\n                        <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23084 title=\"6 occurrences\">#3084</a>}\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">display_mode</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">additional</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>\n                  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">meta_keywords</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">pending</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">moved</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>attribute_family</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">attribute_family_id</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">typeInstance</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n        <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22988 title=\"2 occurrences\">#2988</a><samp data-depth=5 id=sf-dump-1271717441-ref22988 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>178</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1031</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">configurable</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>178</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1031</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">configurable</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>additional</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>images</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2817</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#2991</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>151</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/C9CAErOffwfkXBRYkun6XkFlJCeaZeIGTGhNCXZB.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>151</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/C9CAErOffwfkXBRYkun6XkFlJCeaZeIGTGhNCXZB.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3014</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>152</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/PWAOnhlqNDdR2iLhUzOMp3SHmN4kvkViY0Sfatt5.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>2</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>152</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/PWAOnhlqNDdR2iLhUzOMp3SHmN4kvkViY0Sfatt5.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>2</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3011</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>153</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/mJkU9WAYYJx7mZwApiKjFwiArfb2LFE0I0pHaZZo.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>3</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>153</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/mJkU9WAYYJx7mZwApiKjFwiArfb2LFE0I0pHaZZo.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>3</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3010</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>154</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/AWLd42miv7JuQrPrquyKorZXJNo08hni4Mz0MzSv.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>4</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>154</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/AWLd42miv7JuQrPrquyKorZXJNo08hni4Mz0MzSv.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>4</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3009</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>155</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/WqYcN5H4nznWpte9YFLnm1wFO63UHrbNwSxNoPs3.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>5</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>155</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/WqYcN5H4nznWpte9YFLnm1wFO63UHrbNwSxNoPs3.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>5</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3008</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>167</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/Aq2fXPdZrqjE8jZ5sDfdtxesIJirBhtumkOSutla.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>6</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>167</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/178/Aq2fXPdZrqjE8jZ5sDfdtxesIJirBhtumkOSutla.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>6</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>videos</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2858</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>categories</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Kalnoy\\Nestedset\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Kalnoy\\Nestedset</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2848</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref>#3056</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">translations</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                    \"<span class=sf-dump-key>pivot_product_id</span>\" => <span class=sf-dump-num>178</span>\n                    \"<span class=sf-dump-key>pivot_category_id</span>\" => <span class=sf-dump-num>13</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">logo_url</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">banner_url</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#3025</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"18 characters\">product_categories</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>178</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22859 title=\"6 occurrences\">#2859</a>}\n                      +<span class=sf-dump-public title=\"Public property\">pivotRelated</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22926 title=\"6 occurrences\">#2926</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                    </samp>}\n                    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3058</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23063 title=\"6 occurrences\">#3063</a>}\n                        <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23087 title=\"6 occurrences\">#3087</a>}\n                        <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23086 title=\"6 occurrences\">#3086</a>}\n                        <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23085 title=\"6 occurrences\">#3085</a>}\n                        <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23084 title=\"6 occurrences\">#3084</a>}\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">display_mode</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">additional</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>\n                  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">meta_keywords</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">pending</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">moved</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>attribute_family</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">attribute_family_id</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">typeInstance</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n        <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22987 title=\"2 occurrences\">#2987</a><samp data-depth=5 id=sf-dump-1271717441-ref22987 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>184</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"16 characters\">1031-variant-496</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">simple</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>178</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>184</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"16 characters\">1031-variant-496</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">simple</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>178</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>additional</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>images</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2824</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3019</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>160</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/184/sWGXF20ggNEHfArRqjTwSO8YRk3birLcKCv9CNhE.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>184</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>160</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/184/sWGXF20ggNEHfArRqjTwSO8YRk3birLcKCv9CNhE.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>184</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>videos</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2856</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>categories</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Kalnoy\\Nestedset\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Kalnoy\\Nestedset</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2853</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref>#3053</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">translations</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                    \"<span class=sf-dump-key>pivot_product_id</span>\" => <span class=sf-dump-num>184</span>\n                    \"<span class=sf-dump-key>pivot_category_id</span>\" => <span class=sf-dump-num>13</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">logo_url</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">banner_url</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#3023</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"18 characters\">product_categories</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>184</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>184</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22859 title=\"6 occurrences\">#2859</a>}\n                      +<span class=sf-dump-public title=\"Public property\">pivotRelated</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22926 title=\"6 occurrences\">#2926</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                    </samp>}\n                    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3060</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23063 title=\"6 occurrences\">#3063</a>}\n                        <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23087 title=\"6 occurrences\">#3087</a>}\n                        <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23086 title=\"6 occurrences\">#3086</a>}\n                        <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23085 title=\"6 occurrences\">#3085</a>}\n                        <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23084 title=\"6 occurrences\">#3084</a>}\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">display_mode</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">additional</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>\n                  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">meta_keywords</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">pending</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">moved</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>attribute_family</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">attribute_family_id</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">typeInstance</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n        <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22821 title=\"2 occurrences\">#2821</a><samp data-depth=5 id=sf-dump-1271717441-ref22821 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>185</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"16 characters\">1031-variant-497</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">simple</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>178</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>185</span>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"16 characters\">1031-variant-497</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">simple</span>\"\n            \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>178</span>\n            \"<span class=sf-dump-key>attribute_family_id</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-17 03:24:03</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>additional</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>images</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2974</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\ProductImage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductImage</span></span> {<a class=sf-dump-ref>#3018</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>161</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/185/Rszv79g6k9Z2TchuaSSDs5cZCwL9HuqWsAmT2N9H.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>185</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>161</span>\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n                    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"57 characters\">product/185/Rszv79g6k9Z2TchuaSSDs5cZCwL9HuqWsAmT2N9H.webp</span>\"\n                    \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>185</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">path</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>videos</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2854</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>categories</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Kalnoy\\Nestedset\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Kalnoy\\Nestedset</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2849</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref>#3052</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">translations</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                    \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>logo_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n                    \"<span class=sf-dump-key>_lft</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>_rgt</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>additional</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>banner_path</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 10:08:22</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 01:21:23</span>\"\n                    \"<span class=sf-dump-key>pivot_product_id</span>\" => <span class=sf-dump-num>185</span>\n                    \"<span class=sf-dump-key>pivot_category_id</span>\" => <span class=sf-dump-num>13</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">logo_url</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">banner_url</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#3024</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"18 characters\">product_categories</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>185</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>185</span>\n                        \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>13</span>\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22859 title=\"6 occurrences\">#2859</a>}\n                      +<span class=sf-dump-public title=\"Public property\">pivotRelated</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22926 title=\"6 occurrences\">#2926</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"10 characters\">product_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                    </samp>}\n                    \"<span class=sf-dump-key>translations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3032</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23063 title=\"6 occurrences\">#3063</a>}\n                        <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23087 title=\"6 occurrences\">#3087</a>}\n                        <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23086 title=\"6 occurrences\">#3086</a>}\n                        <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23085 title=\"6 occurrences\">#3085</a>}\n                        <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Category\\Models\\CategoryTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Category\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CategoryTranslation</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref23084 title=\"6 occurrences\">#3084</a>}\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">position</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">display_mode</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">additional</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: <span class=sf-dump-const>null</span>\n                  +<span class=sf-dump-public title=\"Public property\">translatedAttributes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">meta_keywords</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">pending</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">moved</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            \"<span class=sf-dump-key>attribute_family</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">attribute_family_id</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">typeInstance</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n      </samp>]\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">attribute_family</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>794</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">eagerLoadRelations</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22878 title=\"2 occurrences\">#2878</a>}\n        <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22990 title=\"2 occurrences\">#2990</a>}\n        <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22989 title=\"2 occurrences\">#2989</a>}\n        <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22988 title=\"2 occurrences\">#2988</a>}\n        <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22987 title=\"2 occurrences\">#2987</a>}\n        <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Webkul\\Product\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Webkul\\Product\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1271717441-ref22821 title=\"2 occurrences\">#2821</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1034</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"79 characters\">vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>487</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">paginate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Database\\Eloquent\\Builder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>6</span>\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>238</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">paginate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Prettus\\Repository\\Eloquent\\BaseRepository</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>6</span>\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">paginate</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">packages/Webkul/Product/src/Repositories/ProductRepository.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>423</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">paginate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Webkul\\Core\\Eloquent\\Repository</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>6</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">packages/Webkul/Product/src/Repositories/ProductRepository.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>213</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">searchFromDatabase</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Webkul\\Product\\Repositories\\ProductRepository</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>new</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at-desc</span>\"\n        \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>6</span>\"\n        \"<span class=sf-dump-key>channel_id</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>visible_individually</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>url_key</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>293</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">getAll</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Webkul\\Product\\Repositories\\ProductRepository</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>new</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at-desc</span>\"\n        \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>6</span>\"\n        \"<span class=sf-dump-key>channel_id</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>visible_individually</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>532</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"20 characters\">getProductsByOptions</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Webkul\\MLKWebAPI\\Http\\Controllers\\API\\IndexController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"12 characters\">NEW ARRIVALS</span>\"\n        \"<span class=sf-dump-key>filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>new</span>\" => \"<span class=sf-dump-str>1</span>\"\n          \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at-desc</span>\"\n          \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>6</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>124</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"25 characters\">batchGetProductsByOptions</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Webkul\\MLKWebAPI\\Http\\Controllers\\API\\IndexController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>new_arrivals</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"12 characters\">NEW ARRIVALS</span>\"\n          \"<span class=sf-dump-key>filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>new</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at-desc</span>\"\n            \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>6</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>hot_sales</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">HOT SALES</span>\"\n          \"<span class=sf-dump-key>filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"9 characters\">price-asc</span>\"\n            \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>5</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str>0</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>magsafe_accessories</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">MAGSAFE ACCESSORIES</span>\"\n          \"<span class=sf-dump-key>filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>new</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at-desc</span>\"\n            \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>5</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>additional_items</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"27 characters\">ADDITIONAL ITEMS TO EXPLORE</span>\"\n          \"<span class=sf-dump-key>filters</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>new</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"8 characters\">name-asc</span>\"\n            \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>5</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Routing/Controller.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>54</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Webkul\\MLKWebAPI\\Http\\Controllers\\API\\IndexController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>44</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callAction</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Routing\\Controller</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>266</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"62 characters\">[object Webkul\\MLKWebAPI\\Http\\Controllers\\API\\IndexController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>212</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">packages/Webkul/MLKWebAPI/src/Http/Middleware/APIAuthenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>61</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Webkul\\MLKWebAPI\\Http\\Middleware\\APIAuthenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">packages/Webkul/MLKWebAPI/src/Http/Middleware/LocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Webkul\\MLKWebAPI\\Http\\Middleware\\LocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">packages/Webkul/Installer/src/Http/Middleware/CanInstall.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>30</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Webkul\\Installer\\Http\\Middleware\\CanInstall</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">packages/Webkul/Core/src/Http/Middleware/SecureHeaders.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>29</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Webkul\\Core\\Http\\Middleware\\SecureHeaders</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271717441\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["    {\n", "        $class = get_class($model);\n", "\n", "        $instance = new static(\n", "            is_null($type)\n", "                ? \"Call to undefined relationship [{$relation}] on model [{$class}].\"\n", "                : \"Call to undefined relationship [{$relation}] on model [{$class}] of type [{$type}].\",\n"], "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FEloquent%2FRelationNotFoundException.php&line=35", "ajax": false, "filename": "RelationNotFoundException.php", "line": "35"}}]}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 15, "nb_statements": 15, "nb_visible_statements": 15, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.058300000000000005, "accumulated_duration_str": "58.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "api_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Middleware\\LocaleMiddleware.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 127}], "start": **********.775464, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 4.528}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "api_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Middleware\\LocaleMiddleware.php", "line": 31}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 127}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 807}], "start": **********.782883, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 4.528, "width_percent": 0.961}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '22' limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/APIController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\APIController.php", "line": 35}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 103}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7864761, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "mlk", "explain": null, "start_percent": 5.489, "width_percent": 5.043}, {"sql": "select * from `theme_customizations` where `channel_id` = 1 and `status` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 369}, {"index": 18, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 107}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7946632, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 10.532, "width_percent": 0.806}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (9, 14, 15, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 369}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 107}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.79718, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 11.338, "width_percent": 0.892}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 746}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.814484, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 12.23, "width_percent": 3.962}, {"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('new', 'sort', 'limit', 'channel_id', 'status', 'visible_individually', 'url_key')", "type": "query", "params": [], "bindings": ["new", "sort", "limit", "channel_id", "status", "visible_individually", "url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 175}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 292}, {"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 485}], "start": **********.820889, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 16.192, "width_percent": 5.214}, {"sql": "select * from `attributes` where `code` = 'created_at'", "type": "query", "params": [], "bindings": ["created_at"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 383}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}], "start": **********.8261862, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 21.407, "width_percent": 0.497}, {"sql": "select count(*) as aggregate from (select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `new_product_attribute_values` on `products`.`id` = `new_product_attribute_values`.`product_id` and `new_product_attribute_values`.`attribute_id` = 5 left join `product_attribute_values` as `new_variant_attribute_values` on `variants`.`id` = `new_variant_attribute_values`.`product_id` and `new_variant_attribute_values`.`attribute_id` = 5 where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 and ((`new_product_attribute_values`.`boolean_value` in ('1')) or (`new_variant_attribute_values`.`boolean_value` in ('1'))) group by `products`.`id`, `products`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": [1, 5, 5, "1", 3, 7, 1, 8, 1, "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 293}], "start": **********.827779, "duration": 0.024730000000000002, "duration_str": "24.73ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 21.904, "width_percent": 42.419}, {"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `new_product_attribute_values` on `products`.`id` = `new_product_attribute_values`.`product_id` and `new_product_attribute_values`.`attribute_id` = 5 left join `product_attribute_values` as `new_variant_attribute_values` on `variants`.`id` = `new_variant_attribute_values`.`product_id` and `new_variant_attribute_values`.`attribute_id` = 5 where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 and ((`new_product_attribute_values`.`boolean_value` in ('1')) or (`new_variant_attribute_values`.`boolean_value` in ('1'))) group by `products`.`id`, `products`.`id` order by `products`.`created_at` desc limit 6 offset 0", "type": "query", "params": [], "bindings": [1, 5, 5, "1", 3, 7, 1, 8, 1, "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 293}], "start": **********.8536541, "duration": 0.00941, "duration_str": "9.41ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 64.322, "width_percent": 16.141}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (178, 184, 185, 195, 211, 219) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 293}], "start": **********.864432, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 80.463, "width_percent": 3.619}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (178, 184, 185, 195, 211, 219) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 293}], "start": **********.867689, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 84.082, "width_percent": 3.396}, {"sql": "select `categories`.*, `product_categories`.`product_id` as `pivot_product_id`, `product_categories`.`category_id` as `pivot_category_id` from `categories` inner join `product_categories` on `categories`.`id` = `product_categories`.`category_id` where `product_categories`.`product_id` in (178, 184, 185, 195, 211, 219)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 24, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 293}], "start": **********.87169, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 87.479, "width_percent": 6.295}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (13)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 293}], "start": **********.876535, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 93.774, "width_percent": 2.967}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\IndexController.php", "line": 293}], "start": **********.880207, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 96.741, "width_percent": 3.259}]}, "models": {"data": {"Webkul\\Theme\\Models\\ThemeCustomizationTranslation": {"value": 69, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomizationTranslation.php&line=1", "ajax": false, "filename": "ThemeCustomizationTranslation.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomization": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Category\\Models\\Category": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Category\\Models\\CategoryTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategoryTranslation.php&line=1", "ajax": false, "filename": "CategoryTranslation.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}}, "count": 125, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "http://mlk.test/api/mlk/index", "action_name": "mlk.api.home.index", "controller_action": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\IndexController@index", "uri": "GET api/mlk/index", "controller": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\IndexController@index<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FIndexController.php&line=101\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/mlk", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FIndexController.php&line=101\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/MLKWebAPI/src/Http/Controllers/API/IndexController.php:101-180</a>", "middleware": "api_locale, api_auth", "duration": "494ms", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1224830656 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1224830656\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-479546136 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-479546136\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-507656158 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 22|Sa******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Apifox/1.0.0 (https://apifox.com)</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507656158\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-601132797 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-601132797\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1866667809 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 03:10:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866667809\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-514828952 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-514828952\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "http://mlk.test/api/mlk/index", "action_name": "mlk.api.home.index", "controller_action": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\IndexController@index"}, "badge": "500 Internal Server Error"}}